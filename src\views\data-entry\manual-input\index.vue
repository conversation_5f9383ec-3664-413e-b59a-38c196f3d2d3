<template>
  <div class="manual-input-page page-content">
    <!-- 基础信息 -->
    <div class="basic-info-section">
      <h3 class="section-title">基础信息</h3>
      <div class="basic-info-form">
        <div class="form-row">
          <div class="form-item">
            <span class="form-label">网点选择</span>
            <el-select
              v-model="basicInfo.branchId"
              placeholder="南京市江宁区某网点"
              class="form-select"
              @change="handleBranchChange"
            >
              <el-option
                v-for="branch in branchOptions"
                :key="branch.value"
                :label="branch.label"
                :value="branch.value"
              />
            </el-select>
          </div>
          <div class="form-item">
            <span class="form-label">负责人</span>
            <el-input v-model="basicInfo.manager" placeholder="张经理" class="form-input" />
          </div>
          <div class="form-item">
            <span class="form-label">时间粒度</span>
            <el-radio-group
              v-model="basicInfo.timeGranularity"
              class="time-granularity-buttons"
              @change="handleTimeGranularityChange"
            >
              <el-radio-button
                v-for="item in timeGranularityOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="form-item">
            <span class="form-label">日期选择</span>
            <el-date-picker
              v-model="basicInfo.selectedDate"
              :type="datePickerType"
              :placeholder="datePickerPlaceholder"
              :format="datePickerFormat"
              :value-format="datePickerValueFormat"
              :disabled-date="disabledDate"
              :range-separator="basicInfo.timeGranularity === 'day' ? '至' : undefined"
              :start-placeholder="basicInfo.timeGranularity === 'day' ? '开始日期' : undefined"
              :end-placeholder="basicInfo.timeGranularity === 'day' ? '结束日期' : undefined"
              class="form-select"
              @change="handleDateChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 数据录入 -->
    <div class="data-input-section">
      <div class="section-header">
        <h3 class="section-title">数据录入</h3>
        <div class="action-buttons">
          <el-button
            type="primary"
            icon="Download"
            @click="downloadTemplate"
            :loading="downloadLoading"
          >
            下载模版
          </el-button>
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            accept=".xlsx,.xls"
            :show-file-list="false"
            :before-upload="beforeUpload"
            @change="handleFileChange"
            style="display: inline-block; margin-left: 10px"
          >
            <el-button type="success" icon="Upload" :loading="importLoading"> 导入Excel </el-button>
          </el-upload>
          <el-button type="primary" icon="Plus" @click="openAddDialog">
            新增数据
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="data-table-container">
        <el-table
          :data="currentPageData"
          border
          stripe
          class="data-table"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="75" align="center">
            <template #default="{ $index }">
              <span>{{ getRowNumber($index) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="date" label="日期" align="center">
            <template #default="{ row }">
              <span>{{ row.date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="电力（总量）"  align="center">
            <template #default="{ row }">
              <span>{{ getElectricityDisplay(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="天然气（总量）"  align="center">
            <template #default="{ row }">
              <span>{{ getGasDisplay(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="用水（总量）"  align="center">
            <template #default="{ row }">
              <span>{{ getWaterDisplay(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="{ row, $index }">
              <el-button
                type="primary"
                size="small"
                icon="Edit"
                @click="editRecord(row, getActualIndex($index))"
                link
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="deleteRecord(getActualIndex($index))"
                link
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <span class="total-info">共 {{ pagination.total }} 条</span>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 底部操作按钮 -->
      <div class="bottom-actions">
        <el-button @click="resetForm" :loading="resetLoading">重置</el-button>
        <el-button type="primary" @click="saveData" :loading="saveLoading">保存</el-button>
        <el-button type="success" @click="submitData" :loading="submitLoading">提交数据</el-button>
      </div>
    </div>

    <!-- 数据录入弹窗 -->
    <DataEntryDialog
      v-model:visible="dialogVisible"
      :is-edit="isEditMode"
      :edit-data="editData"
      @save="handleDialogSave"
      @close="handleDialogClose"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import * as XLSX from 'xlsx'
  import type { UploadFile } from 'element-plus'
  import DataEntryDialog from './components/DataEntryDialog.vue'

  // 定义数据项接口
  interface DataEntryItem {
    date: string
    // 日期相关字段
    dateType: string
    startDate: string
    endDate: string
    weekRange: string
    monthRange: string
    customRange: any[]
    // 电力相关字段
    electricityTotal: string
    hvacConsumption: string
    lightingConsumption: string
    powerConsumption: string
    specialConsumption: string
    // 水相关字段
    waterTotal: string
    // 天然气相关字段
    gasTotal: string
    // 备注
    remarks: string
  }

  // 基础信息
  const basicInfo = reactive({
    branchId: '',
    manager: '',
    timeGranularity: 'day',
    selectedDate: [] as any
  })

  // 时间粒度选项
  const timeGranularityOptions = [
    { value: 'day', label: '日' },
    { value: 'month', label: '月' },
    { value: 'year', label: '年' }
  ]

  // 网点选项
  const branchOptions = ref([
    { label: '南京市江宁区某网点', value: '1' },
    { label: '南京市鼓楼区某网点', value: '2' },
    { label: '南京市玄武区某网点', value: '3' }
  ])

  // 数据录入列表
  const dataList = ref<DataEntryItem[]>([
    {
      date: '2024-01-15',
      dateType: 'daily',
      startDate: '2024-01-15',
      endDate: '',
      weekRange: '',
      monthRange: '',
      customRange: [],
      electricityTotal: '1280.5',
      hvacConsumption: '500.0',
      lightingConsumption: '300.0',
      powerConsumption: '400.0',
      specialConsumption: '80.5',
      waterTotal: '150.2',
      gasTotal: '320.8',
      remarks: '综合能源数据'
    },
    {
      date: '2024-01-16',
      dateType: 'daily',
      startDate: '2024-01-16',
      endDate: '',
      weekRange: '',
      monthRange: '',
      customRange: [],
      electricityTotal: '1150.3',
      hvacConsumption: '450.0',
      lightingConsumption: '280.0',
      powerConsumption: '350.0',
      specialConsumption: '70.3',
      waterTotal: '135.8',
      gasTotal: '298.5',
      remarks: ''
    }
  ])

  // 分页信息
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 2
  })

  // 选中的行
  const selectedRows = ref<any[]>([])

  // 弹窗相关状态
  const dialogVisible = ref(false)
  const isEditMode = ref(false)
  const editData = ref<DataEntryItem | null>(null)
  const editIndex = ref(-1)

  // 加载状态
  const downloadLoading = ref(false)
  const importLoading = ref(false)
  const resetLoading = ref(false)
  const saveLoading = ref(false)
  const submitLoading = ref(false)

  // 上传组件引用
  const uploadRef = ref()



  // 获取电力数据显示
  const getElectricityDisplay = (row: DataEntryItem) => {
    if (row.electricityTotal && row.electricityTotal.trim() !== '') {
      return `${row.electricityTotal} kWh`
    }
    return '-'
  }

  // 获取天然气数据显示
  const getGasDisplay = (row: DataEntryItem) => {
    if (row.gasTotal && row.gasTotal.trim() !== '') {
      return `${row.gasTotal} m³`
    }
    return '-'
  }

  // 获取用水数据显示
  const getWaterDisplay = (row: DataEntryItem) => {
    if (row.waterTotal && row.waterTotal.trim() !== '') {
      return `${row.waterTotal} m³`
    }
    return '-'
  }

  // 事件处理函数
  const handleTimeGranularityChange = (value: string | number | boolean | undefined) => {
    console.log('时间粒度变化:', value)
    // 清空之前的日期选择
    if (value === 'day') {
      basicInfo.selectedDate = []
    } else {
      basicInfo.selectedDate = ''
    }
  }

  const handleDateChange = (value: any) => {
    console.log('日期选择变化:', value)
  }

  const handleBranchChange = (value: string) => {
    console.log('网点选择变化:', value)
  }

  const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection as any[]
  }

  // 禁用未来日期
  const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
  }



  // 操作函数
  const downloadTemplate = async () => {
    try {
      downloadLoading.value = true

      // 创建模板数据
      const templateData = [
        {
          日期: '2024-01-15',
          '电力总量(kWh)': '1280.5',
          '天然气总量(m³)': '320.8',
          '用水总量(m³)': '150.2',
          备注: '综合能源数据'
        },
        {
          日期: '2024-01-16',
          '电力总量(kWh)': '1150.3',
          '天然气总量(m³)': '298.5',
          '用水总量(m³)': '135.8',
          备注: ''
        }
      ]

      // 创建工作簿
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(templateData)

      // 设置列宽
      const colWidths = [
        { wch: 12 }, // 日期
        { wch: 15 }, // 电力总量
        { wch: 15 }, // 天然气总量
        { wch: 15 }, // 用水总量
        { wch: 20 } // 备注
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '数据录入模板')

      // 生成文件名
      const fileName = `数据录入模板_${new Date().toISOString().split('T')[0]}.xlsx`

      // 下载文件
      XLSX.writeFile(wb, fileName)

      ElMessage.success('模板下载成功')
    } catch (error) {
      console.error('模板下载失败:', error)
      ElMessage.error('模板下载失败，请重试')
    } finally {
      downloadLoading.value = false
    }
  }

  // 文件上传前验证
  const beforeUpload = (file: File) => {
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel'
    const isLt10M = file.size / 1024 / 1024 < 10

    if (!isExcel) {
      ElMessage.error('上传文件只能是 Excel 格式!')
      return false
    }
    if (!isLt10M) {
      ElMessage.error('上传文件大小不能超过 10MB!')
      return false
    }
    return true
  }

  // 处理文件变化
  const handleFileChange = async (uploadFile: UploadFile) => {
    if (!uploadFile.raw) return

    try {
      importLoading.value = true
      const results = await importExcel(uploadFile.raw)

      if (results && results.length > 0) {
        // 转换导入的数据格式
        const importedData = results.map((item: any) => {
          const energyType = convertEnergyType(item['能源类型'])
          const consumption = item['用能数据'] || ''
          const date = item['日期'] || ''

          return {
            date,
            energyType,
            subType: 'total',
            consumption,
            unit: convertUnit(item['计量单位']),
            carbonEmission: '0.00',
            remarks: '',
            dateType: 'daily',
            startDate: date,
            endDate: '',
            weekRange: '',
            monthRange: '',
            customRange: [] as any[],
            electricityTotal: energyType === 'electricity' ? consumption : '',
            hvacConsumption: '',
            lightingConsumption: '',
            powerConsumption: '',
            specialConsumption: '',
            waterTotal: energyType === 'water' ? consumption : '',
            gasTotal: energyType === 'gas' ? consumption : ''
          }
        })

        // 不再计算碳排放当量

        // 添加到数据列表
        dataList.value.push(...importedData)
        pagination.total += importedData.length

        ElMessage.success(`成功导入 ${importedData.length} 条数据`)
      } else {
        ElMessage.warning('Excel文件中没有找到有效数据')
      }
    } catch (error) {
      console.error('Excel导入失败:', error)
      ElMessage.error('Excel导入失败，请检查文件格式')
    } finally {
      importLoading.value = false
      // 清空上传组件
      uploadRef.value?.clearFiles()
    }
  }

  // Excel导入工具函数
  const importExcel = (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = (e) => {
        try {
          const data = e.target?.result
          const workbook = XLSX.read(data, { type: 'array' })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const results = XLSX.utils.sheet_to_json(worksheet)
          resolve(results)
        } catch (error) {
          reject(error)
        }
      }

      reader.onerror = (error) => reject(error)
      reader.readAsArrayBuffer(file)
    })
  }

  // 数据转换函数
  const convertEnergyType = (type: string) => {
    const typeMap: Record<string, string> = {
      电力: 'electricity',
      天然气: 'gas',
      水资源: 'water'
    }
    return typeMap[type] || 'electricity'
  }



  const convertUnit = (unit: string) => {
    const unitMap: Record<string, string> = {
      kWh: 'kWh',
      'm³': 'm³',
      吨: 'ton'
    }
    return unitMap[unit] || 'kWh'
  }



  // 弹窗相关方法
  const openAddDialog = () => {
    isEditMode.value = false
    editData.value = null
    editIndex.value = -1
    dialogVisible.value = true
  }

  const editRecord = (row: DataEntryItem, index: number) => {
    isEditMode.value = true
    editData.value = { ...row }
    editIndex.value = index
    dialogVisible.value = true
  }

  // 生成日期显示文本
  const generateDateDisplay = (data: DataEntryItem): string => {
    switch (data.dateType) {
      case 'daily':
        return data.startDate
      case 'weekly':
        if (data.weekRange) {
          const date = new Date(data.weekRange)
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const firstDayOfMonth = new Date(year, date.getMonth(), 1)
          const firstWeekday = firstDayOfMonth.getDay() === 0 ? 7 : firstDayOfMonth.getDay()
          const dayOfMonth = date.getDate()
          const weekOfMonth = Math.ceil((dayOfMonth + firstWeekday - 1) / 7)
          return `${year}年 ${month}月 第${weekOfMonth}周`
        }
        return data.weekRange
      case 'monthly':
        if (data.monthRange) {
          const [year, month] = data.monthRange.split('-')
          return `${year}年 ${month}月`
        }
        return data.monthRange
      case 'custom':
        if (data.customRange && data.customRange.length === 2) {
          return `${data.customRange[0]} 至 ${data.customRange[1]}`
        }
        return ''
      default:
        return ''
    }
  }

  const handleDialogSave = (data: DataEntryItem) => {
    // 检查日期冲突
    if (!isEditMode.value && !checkDateConflict(data)) {
      return // 如果有冲突，不保存
    }

    // 生成日期显示
    const processedData = {
      ...data,
      date: generateDateDisplay(data)
    }

    if (isEditMode.value && editIndex.value >= 0) {
      // 编辑模式：更新现有数据
      dataList.value[editIndex.value] = processedData
      ElMessage.success('数据更新成功')
    } else {
      // 新增模式：添加新数据
      dataList.value.push(processedData)
      pagination.total++
      ElMessage.success('数据添加成功')
    }
    dialogVisible.value = false
  }

  const handleDialogClose = () => {
    dialogVisible.value = false
    isEditMode.value = false
    editData.value = null
    editIndex.value = -1
  }



  const deleteRecord = (index: number) => {
    ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      dataList.value.splice(index, 1)
      pagination.total--
      ElMessage.success('删除成功')
    })
  }

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.currentPage = 1
  }

  const handleCurrentChange = (page: number) => {
    pagination.currentPage = page
  }

  // 日期选择器相关计算属性
  const datePickerType = computed(() => {
    switch (basicInfo.timeGranularity) {
      case 'day':
        return 'daterange'
      case 'month':
        return 'month'
      case 'year':
        return 'year'
      default:
        return 'daterange'
    }
  })

  const datePickerPlaceholder = computed(() => {
    switch (basicInfo.timeGranularity) {
      case 'day':
        return '选择日期范围'
      case 'month':
        return '选择月份'
      case 'year':
        return '选择年份'
      default:
        return '选择日期范围'
    }
  })

  const datePickerFormat = computed(() => {
    switch (basicInfo.timeGranularity) {
      case 'day':
        return 'YYYY年 MM月 DD日'
      case 'month':
        return 'YYYY年 MM月'
      case 'year':
        return 'YYYY年'
      default:
        return 'YYYY年 MM月 DD日'
    }
  })

  const datePickerValueFormat = computed(() => {
    switch (basicInfo.timeGranularity) {
      case 'day':
        return 'YYYY-MM-DD'
      case 'month':
        return 'YYYY-MM'
      case 'year':
        return 'YYYY'
      default:
        return 'YYYY-MM-DD'
    }
  })

  // 获取当前页数据
  const currentPageData = computed(() => {
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    return dataList.value.slice(start, end)
  })

  // 获取实际索引（考虑分页）
  const getActualIndex = (pageIndex: number) => {
    return (pagination.currentPage - 1) * pagination.pageSize + pageIndex
  }

  // 获取行号（考虑分页）
  const getRowNumber = (pageIndex: number) => {
    const number = (pagination.currentPage - 1) * pagination.pageSize + pageIndex + 1
    return `${number}`
  }

  // 重置表单
  const resetForm = async () => {
    try {
      await ElMessageBox.confirm('确定要重置表单吗？此操作将清空所有数据。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      resetLoading.value = true

      // 重置基础信息
      basicInfo.branchId = ''
      basicInfo.manager = ''
      basicInfo.timeGranularity = 'day'
      basicInfo.selectedDate = []

      // 重置数据列表
      dataList.value = []
      pagination.total = 0
      pagination.currentPage = 1

      ElMessage.success('重置成功')
    } catch {
      // 用户取消操作
    } finally {
      resetLoading.value = false
    }
  }

  // 保存数据
  const saveData = async () => {
    try {
      saveLoading.value = true

      // 验证基础信息
      if (!basicInfo.branchId) {
        ElMessage.warning('请选择网点')
        return
      }

      if (!basicInfo.manager) {
        ElMessage.warning('请输入负责人')
        return
      }

      // 这里可以调用API保存数据
      // await api.saveData({ basicInfo, dataList: dataList.value })

      ElMessage.success('数据保存成功')

      // 保存成功后切换到只读模式
      isEditMode.value = false
    } catch (error) {
      console.error('保存数据失败:', error)
      ElMessage.error('保存数据失败，请重试')
    } finally {
      saveLoading.value = false
    }
  }

  // 提交数据
  const submitData = async () => {
    try {
      // 验证数据
      if (dataList.value.length === 0) {
        ElMessage.warning('请先添加数据')
        return
      }

      if (!basicInfo.branchId) {
        ElMessage.warning('请选择网点')
        return
      }

      if (!basicInfo.manager) {
        ElMessage.warning('请输入负责人')
        return
      }

      // 验证数据完整性
      const invalidRows = dataList.value.filter((item) => {
        if (!item.date) {
          return true
        }

        // 验证至少有一种能源数据
        const hasElectricity = item.electricityTotal && item.electricityTotal.trim() !== ''
        const hasWater = item.waterTotal && item.waterTotal.trim() !== ''
        const hasGas = item.gasTotal && item.gasTotal.trim() !== ''

        if (!hasElectricity && !hasWater && !hasGas) {
          return true
        }

        return false
      })

      if (invalidRows.length > 0) {
        ElMessage.warning('存在未完整填写的数据行，请检查后再提交')
        return
      }

      await ElMessageBox.confirm('确定要提交数据吗？提交后将无法修改。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })

      submitLoading.value = true

      // 这里可以调用API提交数据
      // await api.submitData({ basicInfo, dataList: dataList.value })

      ElMessage.success('数据提交成功')

      // 提交成功后可以清空表单或跳转到其他页面
      // resetForm()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交数据失败:', error)
        ElMessage.error('提交数据失败，请重试')
      }
    } finally {
      submitLoading.value = false
    }
  }

  onMounted(() => {
    // 初始化数据
    console.log('手动录入页面初始化')
  })
</script>

<style scoped lang="scss">
  .manual-input-page {
    min-height: 100vh;
    padding: 20px;
    background-color: #f5f5f5;

    .basic-info-section {
      padding: 20px;
      margin-bottom: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgb(0 0 0 / 10%);

      .section-title {
        padding-bottom: 10px;
        margin: 0 0 20px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid #e6e6e6;
      }

      .basic-info-form {
        .form-row {
          display: flex;
          gap: 30px;
          align-items: center;

          .form-item {
            display: flex;
            gap: 10px;
            align-items: center;

            .form-label {
              min-width: 80px;
              font-size: 14px;
              color: #666;
              white-space: nowrap;
            }

            .form-select,
            .form-input {
              width: 200px;
            }

            .time-granularity-buttons {
              width: 200px;

              .el-radio-button {
                margin-right: 0;
              }

              .el-radio-button__inner {
                border-radius: 4px;
                margin-right: 8px;
                padding: 8px 16px;
                font-size: 14px;
                transition: all 0.3s;

                &:hover {
                  color: #409eff;
                  border-color: #409eff;
                }
              }

              .el-radio-button:first-child .el-radio-button__inner {
                border-radius: 4px;
              }

              .el-radio-button:last-child .el-radio-button__inner {
                border-radius: 4px;
              }

              .el-radio-button.is-active .el-radio-button__inner {
                background-color: #409eff;
                border-color: #409eff;
                color: #fff;
              }
            }
          }
        }
      }
    }

    .data-input-section {
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgb(0 0 0 / 10%);

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .section-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .action-buttons {
          display: flex;
          gap: 10px;
        }
      }

      .data-table-container {
        margin-bottom: 20px;

        .data-table {
          width: 100%;

          :deep(.el-table__header) {
            background-color: #f8f9fa;
          }

          :deep(.el-table__row) {
            &:hover {
              background-color: #f5f7fa;
            }
          }

          :deep(.el-input__inner) {
            height: 32px;
          }

          :deep(.el-select) {
            width: 100%;
          }

          :deep(.el-date-editor) {
            width: 100%;
          }
        }
      }

      .pagination-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0;
        margin-bottom: 20px;

        .total-info {
          font-size: 14px;
          color: #666;
        }
      }

      .bottom-actions {
        display: flex;
        gap: 20px;
        justify-content: flex-end;
        padding-top: 20px;
        border-top: 1px solid #e6e6e6;
      }
    }
  }

  // 响应式设计
  @media (width <= 1200px) {
    .manual-input-page {
      .basic-info-section {
        .basic-info-form {
          .form-row {
            flex-direction: column;
            gap: 15px;
            align-items: flex-start;

            .form-item {
              justify-content: space-between;
              width: 100%;

              .form-select,
              .form-input {
                width: 300px;
              }
            }
          }
        }
      }

      .data-input-section {
        .section-header {
          flex-direction: column;
          gap: 15px;
          align-items: flex-start;

          .action-buttons {
            flex-wrap: wrap;
            justify-content: flex-start;
            width: 100%;
          }
        }
      }
    }
  }

  @media (width <= 768px) {
    .manual-input-page {
      padding: 10px;

      .basic-info-section,
      .data-input-section {
        padding: 15px;
      }

      .basic-info-section {
        .basic-info-form {
          .form-row {
            .form-item {
              flex-direction: column;
              gap: 5px;
              align-items: flex-start;

              .form-select,
              .form-input {
                width: 100%;
              }
            }
          }
        }
      }

      .data-input-section {
        .data-table-container {
          overflow-x: auto;
        }

        .pagination-container {
          flex-direction: column;
          gap: 10px;
          align-items: center;
        }

        .bottom-actions {
          flex-direction: column;
          gap: 10px;

          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
</style>
