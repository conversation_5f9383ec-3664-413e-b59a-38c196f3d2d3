<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="data-entry-form"
    >
      <!-- 日期类型选择 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="日期类型" prop="dateType">
            <el-select
              v-model="formData.dateType"
              placeholder="请选择日期类型"
              style="width: 100%"
              @change="handleDateTypeChange"
            >
              <el-option label="日" value="daily" />
              <el-option label="周" value="weekly" />
              <el-option label="月" value="monthly" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 日期选择 -->
      <el-row :gutter="20" v-if="formData.dateType">
        <el-col :span="24">
          <!-- 日选择 -->
          <el-form-item v-if="formData.dateType === 'daily'" label="选择日期" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              placeholder="选择日期"
              format="YYYY年 MM月 DD日"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 周选择 -->
          <el-form-item v-else-if="formData.dateType === 'weekly'" label="选择周" prop="weekRange">
            <div class="week-picker-wrapper">
              <el-date-picker
                v-model="formData.weekRange"
                type="week"
                placeholder="选择周"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                style="width: 100%"
                @change="handleWeekChange"
              />
              <div v-if="weekDisplayText" class="week-display-text">
                {{ weekDisplayText }}
              </div>
            </div>
          </el-form-item>

          <!-- 月选择 -->
          <el-form-item v-else-if="formData.dateType === 'monthly'" label="选择月份" prop="monthRange">
            <el-date-picker
              v-model="formData.monthRange"
              type="month"
              placeholder="选择月份"
              format="YYYY年 MM月"
              value-format="YYYY-MM"
              :disabled-date="disabledDate"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 自定义日期范围 -->
          <el-form-item v-else-if="formData.dateType === 'custom'" label="自定义日期" prop="customRange">
            <el-date-picker
              v-model="formData.customRange"
              type="daterange"
              format="YYYY年 MM月 DD日"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 能源数据录入 - 按顺序展示 -->
      <template v-if="isDateSelected">
        <!-- 电力数据录入 -->
        <div class="energy-section">
          <h4 class="energy-title">电力数据</h4>
          <!-- 总用电 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="总用电" prop="electricityTotal">
                <el-input
                  v-model="formData.electricityTotal"
                  placeholder="请输入总用电量"
                >
                  <template #append>kWh</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 分项用电 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="暖通空调">
                <el-input
                  v-model="formData.hvacConsumption"
                  placeholder="请输入暖通空调用电量"
                >
                  <template #append>kWh</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="照明插座">
                <el-input
                  v-model="formData.lightingConsumption"
                  placeholder="请输入照明插座用电量"
                >
                  <template #append>kWh</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="动力用电">
                <el-input
                  v-model="formData.powerConsumption"
                  placeholder="请输入动力用电量"
                >
                  <template #append>kWh</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="特殊用电">
                <el-input
                  v-model="formData.specialConsumption"
                  placeholder="请输入特殊用电量"
                >
                  <template #append>kWh</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 用水数据录入 -->
        <div class="energy-section">
          <h4 class="energy-title">用水数据</h4>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="总用水" prop="waterTotal">
                <el-input
                  v-model="formData.waterTotal"
                  placeholder="请输入总用水量"
                >
                  <template #append>m³</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 天然气数据录入 -->
        <div class="energy-section">
          <h4 class="energy-title">天然气数据</h4>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="总用气" prop="gasTotal">
                <el-input
                  v-model="formData.gasTotal"
                  placeholder="请输入总用气量"
                >
                  <template #append>m³</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </template>


    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

  // 定义接口
  interface DataEntryItem {
    date: string
    // 日期相关字段
    dateType: string
    startDate: string
    endDate: string
    weekRange: string
    monthRange: string
    customRange: any[]
    // 电力相关字段
    electricityTotal: string
    hvacConsumption: string
    lightingConsumption: string
    powerConsumption: string
    specialConsumption: string
    // 水相关字段
    waterTotal: string
    // 天然气相关字段
    gasTotal: string
    // 备注
    remarks: string
  }

  // 定义 props
  interface Props {
    visible: boolean
    isEdit?: boolean
    editData?: DataEntryItem | null
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    isEdit: false,
    editData: null
  })

  // 定义 emits
  const emit = defineEmits<{
    'update:visible': [value: boolean]
    save: [data: DataEntryItem]
    close: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const weekDisplayText = ref('')

  // 表单数据
  const formData = reactive<DataEntryItem>({
    date: '',
    dateType: '',
    startDate: '',
    endDate: '',
    weekRange: '',
    monthRange: '',
    customRange: [],
    electricityTotal: '',
    hvacConsumption: '',
    lightingConsumption: '',
    powerConsumption: '',
    specialConsumption: '',
    waterTotal: '',
    gasTotal: '',
    remarks: ''
  })

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogTitle = computed(() => {
    return props.isEdit ? '编辑数据' : '新增数据'
  })

  // 判断是否已选择日期
  const isDateSelected = computed(() => {
    if (!formData.dateType) return false

    switch (formData.dateType) {
      case 'daily':
        return !!formData.startDate
      case 'weekly':
        return !!formData.weekRange
      case 'monthly':
        return !!formData.monthRange
      case 'custom':
        return formData.customRange && formData.customRange.length === 2
      default:
        return false
    }
  })

  // 表单验证规则
  const formRules: FormRules = {
    dateType: [
      { required: true, message: '请选择日期类型', trigger: 'change' }
    ],
    startDate: [
      { required: true, message: '请选择日期', trigger: 'change' }
    ],
    weekRange: [
      { required: true, message: '请选择周', trigger: 'change' }
    ],
    monthRange: [
      { required: true, message: '请选择月份', trigger: 'change' }
    ],
    customRange: [
      { required: true, message: '请选择自定义日期范围', trigger: 'change' }
    ],
    electricityTotal: [
      { required: true, message: '请输入总用电量', trigger: 'blur' }
    ]
  }

  // 禁用未来日期
  const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
  }



  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      date: '',
      dateType: '',
      startDate: '',
      endDate: '',
      weekRange: '',
      monthRange: '',
      customRange: [],
      electricityTotal: '',
      hvacConsumption: '',
      lightingConsumption: '',
      powerConsumption: '',
      specialConsumption: '',
      waterTotal: '',
      gasTotal: '',
      remarks: ''
    })
    formRef.value?.clearValidate()
  }

  // 事件处理函数
  const handleDateTypeChange = () => {
    // 清空之前的日期选择
    formData.startDate = ''
    formData.endDate = ''
    formData.weekRange = ''
    formData.monthRange = ''
    formData.customRange = []
    weekDisplayText.value = ''
  }

  // 处理周选择变化
  const handleWeekChange = (value: string) => {
    if (value) {
      const date = new Date(value)
      const year = date.getFullYear()
      const month = date.getMonth() + 1

      // 计算是该月的第几周
      const firstDayOfMonth = new Date(year, date.getMonth(), 1)
      const firstWeekday = firstDayOfMonth.getDay() === 0 ? 7 : firstDayOfMonth.getDay() // 周日为7
      const dayOfMonth = date.getDate()
      const weekOfMonth = Math.ceil((dayOfMonth + firstWeekday - 1) / 7)

      weekDisplayText.value = `${year}年 ${month}月 第${weekOfMonth}周`
    } else {
      weekDisplayText.value = ''
    }
  }

  // 初始化表单数据
  const initFormData = () => {
    if (props.isEdit && props.editData) {
      Object.assign(formData, { ...props.editData })
      // 如果是编辑模式且有周数据，更新显示文本
      if (formData.dateType === 'weekly' && formData.weekRange) {
        handleWeekChange(formData.weekRange)
      }
    } else {
      resetForm()
    }
  }

  // 自定义验证所有必要字段
  const validateAllFields = () => {
    const errors: string[] = []

    // 验证日期类型
    if (!formData.dateType) errors.push('请选择日期类型')

    // 验证日期选择
    if (formData.dateType && !isDateSelected.value) {
      switch (formData.dateType) {
        case 'daily':
          errors.push('请选择日期')
          break
        case 'weekly':
          errors.push('请选择周')
          break
        case 'monthly':
          errors.push('请选择月份')
          break
        case 'custom':
          errors.push('请选择自定义日期范围')
          break
      }
    }

    // 验证至少填写一种能源数据
    const hasElectricity = formData.electricityTotal && formData.electricityTotal.trim() !== ''
    const hasWater = formData.waterTotal && formData.waterTotal.trim() !== ''
    const hasGas = formData.gasTotal && formData.gasTotal.trim() !== ''

    if (!hasElectricity && !hasWater && !hasGas) {
      errors.push('请至少填写一种能源数据')
    }

    // 验证电力数据格式（如果填写了）
    if (hasElectricity) {
      if (!/^\d+(\.\d{1,2})?$/.test(formData.electricityTotal)) {
        errors.push('总用电量格式不正确（最多2位小数）')
      }

      // 验证分项用电（如果填写了，格式要正确）
      const subFields = [
        { field: formData.hvacConsumption, name: '暖通空调用电量' },
        { field: formData.lightingConsumption, name: '照明插座用电量' },
        { field: formData.powerConsumption, name: '动力用电量' },
        { field: formData.specialConsumption, name: '特殊用电量' }
      ]

      subFields.forEach(({ field, name }) => {
        if (field && field.trim() !== '' && !/^\d+(\.\d{1,2})?$/.test(field)) {
          errors.push(`${name}格式不正确（最多2位小数）`)
        }
      })
    }

    // 验证用水数据格式（如果填写了）
    if (hasWater && !/^\d+(\.\d{1,2})?$/.test(formData.waterTotal)) {
      errors.push('总用水量格式不正确（最多2位小数）')
    }

    // 验证天然气数据格式（如果填写了）
    if (hasGas && !/^\d+(\.\d{1,2})?$/.test(formData.gasTotal)) {
      errors.push('总用气量格式不正确（最多2位小数）')
    }

    return errors
  }

  // 保存数据
  const handleSave = async () => {
    if (!formRef.value) return

    try {
      // 先验证基础字段
      await formRef.value.validate()

      // 再验证所有必要字段
      const validationErrors = validateAllFields()
      if (validationErrors.length > 0) {
        ElMessage.error(validationErrors[0])
        return
      }

      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      emit('save', { ...formData })
      ElMessage.success(props.isEdit ? '数据更新成功' : '数据保存成功')
      handleClose()
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 关闭弹窗
  const handleClose = () => {
    resetForm()
    emit('close')
    emit('update:visible', false)
  }

  // 监听弹窗显示状态
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        initFormData()
      }
    },
    { immediate: true }
  )
</script>

<style scoped lang="scss">
  .data-entry-form {
    padding: 10px 0;
    width: 100%;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    :deep(.el-input-group__append) {
      background-color: #f5f7fa;
      border-left: 1px solid #dcdfe6;
      color: #909399;
      font-size: 12px;
    }

    :deep(.el-textarea__inner) {
      resize: none;
    }

    :deep(.el-form-item) {
      margin-bottom: 18px;
    }

    .week-picker-wrapper {
      position: relative;

      .week-display-text {
        position: absolute;
        top: 50%;
        left: 12px;
        right: 40px;
        transform: translateY(-50%);
        color: #606266;
        font-size: 14px;
        pointer-events: none;
        z-index: 10;
        background: transparent;
        padding: 0 4px;
        white-space: nowrap;
        overflow: hidden;
      }

      :deep(.el-input__inner) {
        color: transparent !important;
        text-shadow: none !important;
      }

      :deep(.el-input__prefix) {
        display: none !important;
      }

      :deep(.el-input__wrapper) {
        position: relative;
      }

      :deep(.el-input) {
        .el-input__wrapper {
          border: 1px solid #dcdfe6;
          border-radius: 4px;

          &:hover {
            border-color: #c0c4cc;
          }

          &.is-focus {
            border-color: #409eff;
          }
        }
      }
    }

    .energy-section {
      margin-bottom: 24px;
      padding: 16px;
      background-color: #fafafa;
      border-radius: 6px;
      border: 1px solid #e6e6e6;

      .energy-title {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #409eff;
        border-bottom: 1px solid #e6e6e6;
        padding-bottom: 8px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
